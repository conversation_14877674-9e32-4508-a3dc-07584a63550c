#!/usr/bin/env python3
"""
Test script for the new volatility dots functionality in the Rolling Dispersion of CSSD by Currency chart.
Tests the logic for detecting when at least 3 pairs of a currency have absolute log returns above sd1 for more than 3 periods.
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from dispersion_charts import DispersionChartCreator
from config import CURRENCY_PAIRS, CURRENCY_COLORS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data():
    """Create synthetic test data to verify the volatility dots functionality"""
    
    # Create time index for the last 2 hours (120 minutes)
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=2)
    time_index = pd.date_range(start=start_time, end=end_time, freq='1min')
    
    # Create synthetic normalized returns data
    np.random.seed(42)  # For reproducible results
    
    # Create base returns with some volatility
    base_returns = np.random.normal(0, 0.001, len(time_index))
    
    # Create returns for all currency pairs
    returns_data = {}
    
    for pair in CURRENCY_PAIRS:
        # Add some pair-specific noise
        pair_returns = base_returns + np.random.normal(0, 0.0005, len(time_index))
        
        # Add some periods of high volatility for testing
        # Make some periods have higher absolute returns to trigger the dots
        if pair in ['EURUSD', 'GBPUSD', 'AUDUSD']:  # USD pairs
            # Add high volatility periods around minute 60-80
            high_vol_start = 60
            high_vol_end = 80
            if high_vol_end < len(time_index):
                pair_returns[high_vol_start:high_vol_end] += np.random.normal(0, 0.005, high_vol_end - high_vol_start)
        
        if pair in ['EURJPY', 'GBPJPY', 'AUDJPY']:  # JPY pairs  
            # Add high volatility periods around minute 40-60
            high_vol_start = 40
            high_vol_end = 60
            if high_vol_end < len(time_index):
                pair_returns[high_vol_start:high_vol_end] += np.random.normal(0, 0.005, high_vol_end - high_vol_start)
        
        returns_data[pair] = pair_returns
    
    # Create DataFrame
    returns_df = pd.DataFrame(returns_data, index=time_index)
    
    logger.info(f"Created test data with {len(returns_df)} time points and {len(returns_df.columns)} currency pairs")
    return returns_df

def test_volatility_dots():
    """Test the volatility dots functionality"""
    
    logger.info("Starting volatility dots test...")
    
    # Create test data
    normalized_returns_ts = create_test_data()
    
    # Create dispersion charts instance
    dispersion_charts = DispersionChartCreator()
    
    # Create a test figure
    fig = make_subplots(
        rows=1, cols=1,
        subplot_titles=("Test: Rolling Dispersion with Volatility Dots",)
    )
    
    # Test the dot functionality for USD currency
    currency = 'USD'
    pairs = [pair for pair in CURRENCY_PAIRS if 'USD' in pair]
    color = CURRENCY_COLORS.get(currency, '#1f77b4')
    
    logger.info(f"Testing volatility dots for {currency} with {len(pairs)} pairs")
    
    # Call the new method
    dispersion_charts._add_currency_volatility_dots(
        fig, normalized_returns_ts, currency, pairs, color, row=1, col=1
    )
    
    # Test for JPY currency as well
    currency = 'JPY'
    pairs = [pair for pair in CURRENCY_PAIRS if 'JPY' in pair]
    color = CURRENCY_COLORS.get(currency, '#ff7f0e')
    
    logger.info(f"Testing volatility dots for {currency} with {len(pairs)} pairs")
    
    # Call the new method
    dispersion_charts._add_currency_volatility_dots(
        fig, normalized_returns_ts, currency, pairs, color, row=1, col=1
    )
    
    # Add some sample data to the chart for context
    fig.add_trace(go.Scatter(
        x=normalized_returns_ts.index,
        y=np.random.normal(0.5, 0.1, len(normalized_returns_ts)),
        mode='lines',
        name='Sample Data',
        line=dict(color='gray', width=1)
    ))
    
    # Update layout
    fig.update_layout(
        title="Volatility Dots Test - USD and JPY Currencies",
        xaxis_title="Time",
        yaxis_title="Value",
        height=600,
        showlegend=True
    )
    
    # Save the test chart
    output_file = "volatility_dots_test.html"
    fig.write_html(output_file)
    logger.info(f"Test chart saved to {output_file}")
    
    # Print some statistics about the test data
    logger.info("Test data statistics:")
    for currency in ['USD', 'JPY']:
        pairs = [pair for pair in CURRENCY_PAIRS if currency in pair]
        available_pairs = [pair for pair in pairs if pair in normalized_returns_ts.columns]
        
        if available_pairs:
            currency_returns = normalized_returns_ts[available_pairs]
            abs_returns = currency_returns.abs()
            
            # Calculate rolling std
            rolling_std = abs_returns.rolling(window=20, min_periods=1).std()
            above_sd1 = abs_returns > rolling_std
            pairs_above_sd1_count = above_sd1.sum(axis=1)
            
            periods_with_3plus = (pairs_above_sd1_count >= 3).sum()
            logger.info(f"{currency}: {periods_with_3plus} periods with 3+ pairs above SD1")

if __name__ == "__main__":
    test_volatility_dots()
